"""
真正的多智能体工作流框架
核心理念：Agent 之间通过消息传递进行动态交互，而不是固定的工作流节点
每个 Agent 都是自主的，可以决定调用工具、与其他 Agent 对话或结束任务
"""
import json
from typing import Annotated, Optional, Dict, Any, List, Literal
from langchain_core.messages import AnyMessage, AIMessage, HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel, Field
from typing_extensions import TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# 简化的状态定义 - 只保留核心的消息传递
class AgentState(TypedDict):
    """智能体状态 - 以消息为中心"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]  # 全局上下文


# 工具定义 - 使用 @tool 装饰器
@tool
async def vector_database_search(query: str) -> dict:
    """
    在违规案例向量数据库中进行搜索。
    当你需要查询具体的违规案例、公司信息或相关法规时，使用此工具。
    
    Args:
        query: 搜索查询语句
        
    Returns:
        包含搜索结果的字典
    """
    try:
        logger.info(f"[VectorTool] 执行向量搜索: {query}")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": f"🔍 正在向量数据库中搜索: {query}"}, ensure_ascii=False))
        except Exception:
            pass
            
        result_dict = await execute_multi_query_rerank_search(query)
        
        # 推送向量检索数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({'AI_AGENT_VECTOR': result_dict}, ensure_ascii=False))
        except Exception:
            pass
            
        return result_dict
        
    except Exception as e:
        logger.error(f"[VectorTool] 搜索失败: {e}")
        return {"error": f"搜索失败: {str(e)}"}


@tool
async def generate_chart(data_summary: str) -> dict:
    """
    根据数据分析结果生成可视化图表。
    当分析结果适合用图表展示时，使用此工具。
    
    Args:
        data_summary: 数据分析摘要
        
    Returns:
        包含图表配置的字典
    """
    try:
        logger.info(f"[ChartTool] 生成图表: {data_summary[:100]}...")
        
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📈 正在生成可视化图表..."}, ensure_ascii=False))
        except Exception:
            pass
            
        # 这里可以调用您现有的图表生成逻辑
        # 简化示例
        chart_config = {
            "type": "bar",
            "title": "违规案例分析图表",
            "data": data_summary
        }
        
        # 推送图表数据
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_CHART": chart_config}, ensure_ascii=False))
        except Exception:
            pass
            
        return chart_config
        
    except Exception as e:
        logger.error(f"[ChartTool] 图表生成失败: {e}")
        return {"error": f"图表生成失败: {str(e)}"}


# 工具列表
tools = [vector_database_search, generate_chart]
tool_node = ToolNode(tools)


class SupervisorAgent:
    """监督者智能体 - 负责意图识别和任务路由"""
    
    def __init__(self, llm):
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能助手的监督者，负责识别用户意图并决定处理路径。

你需要判断用户输入属于以下哪种类型：

1. **simple_chat（简单对话）**：
   - 问候语、感谢语、闲聊等
   - 直接回复，不需要调用其他智能体

2. **query_task（查询任务）**：
   - 任何需要查询、搜索、分析的请求
   - 需要委托给 PlannerAgent 进行处理

请根据用户输入，选择合适的处理方式：
- 如果是简单对话，直接生成友好的回复
- 如果是查询任务，回复 "DELEGATE_TO_PLANNER" 表示委托给规划智能体

用户输入：{user_input}"""),
        ])
    
    async def process(self, messages: List[AnyMessage]) -> AIMessage:
        """处理用户输入"""
        try:
            user_input = messages[-1].content if messages else ""
            
            # 发送流程信息
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "🤔 监督者正在分析您的需求..."}, ensure_ascii=False))
            except Exception:
                pass
            
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            
            return AIMessage(content=response.content)
            
        except Exception as e:
            logger.error(f"[SupervisorAgent] 处理失败: {e}")
            return AIMessage(content="DELEGATE_TO_PLANNER")  # 默认委托给规划者


class PlannerAgent:
    """规划智能体 - 负责分析问题并决定调用哪些工具"""
    
    def __init__(self, llm):
        self.llm_with_tools = llm.bind_tools(tools)
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的违规案例查询分析师。你的任务是：

1. **理解用户需求**：分析用户想要什么信息
2. **制定查询策略**：决定需要搜索什么内容
3. **调用合适的工具**：使用 vector_database_search 工具进行搜索
4. **分析结果**：基于搜索结果，决定是否需要更多信息或生成图表
5. **提供最终答案**：整合所有信息，给出专业的回答

可用工具：
- vector_database_search: 搜索违规案例数据库
- generate_chart: 生成可视化图表

工作流程：
1. 如果用户问题需要数据支持，先调用 vector_database_search
2. 分析搜索结果，判断是否需要更多搜索
3. 如果数据适合可视化，调用 generate_chart
4. 最后提供综合分析和回答

重要：
- 每次只调用一个工具，等待结果后再决定下一步
- 如果搜索结果不够充分，可以用不同关键词再次搜索
- 始终基于实际搜索结果回答，不要编造信息"""),
            ("placeholder", "{messages}"),
        ])
    
    async def process(self, messages: List[AnyMessage]) -> AIMessage:
        """处理消息并决定下一步行动"""
        try:
            # 发送流程信息
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "🧠 规划智能体正在制定查询策略..."}, ensure_ascii=False))
            except Exception:
                pass
            
            chain = self.prompt | self.llm_with_tools
            response = await chain.ainvoke({"messages": messages})
            
            return response
            
        except Exception as e:
            logger.error(f"[PlannerAgent] 处理失败: {e}")
            return AIMessage(content=f"处理请求时出现错误：{str(e)}")


class AgenticWorkflowFramework:
    """真正的智能体工作流框架"""
    
    def __init__(self, llm):
        self.llm = llm
        
        # 初始化智能体
        self.supervisor = SupervisorAgent(llm)
        self.planner = PlannerAgent(llm)
        
        # 构建图
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建智能体交互图"""
        graph = StateGraph(AgentState)
        
        # 添加节点
        graph.add_node("supervisor", self._supervisor_node)
        graph.add_node("planner", self._planner_node)
        graph.add_node("tools", tool_node)
        
        # 设置流程
        graph.add_edge(START, "supervisor")
        
        # 监督者的条件路由
        graph.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "planner": "planner",
                "end": END
            }
        )
        
        # 规划者的条件路由
        graph.add_conditional_edges(
            "planner",
            self._should_continue,
            {
                "tools": "tools",
                "end": END
            }
        )
        
        # 工具执行后返回规划者
        graph.add_edge("tools", "planner")
        
        return graph
    
    async def _supervisor_node(self, state: AgentState) -> Dict[str, Any]:
        """监督者节点"""
        messages = state.get("messages", [])
        response = await self.supervisor.process(messages)
        return {"messages": [response]}
    
    async def _planner_node(self, state: AgentState) -> Dict[str, Any]:
        """规划者节点"""
        messages = state.get("messages", [])
        response = await self.planner.process(messages)
        return {"messages": [response]}
    
    def _supervisor_router(self, state: AgentState) -> Literal["planner", "end"]:
        """监督者路由决策"""
        last_message = state["messages"][-1]
        if "DELEGATE_TO_PLANNER" in last_message.content:
            return "planner"
        return "end"
    
    def _should_continue(self, state: AgentState) -> Literal["tools", "end"]:
        """判断是否需要继续调用工具"""
        last_message = state["messages"][-1]
        
        # 如果最后一条消息包含工具调用，则路由到工具节点
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "tools"
        
        return "end"
    
    def compile(self, **kwargs):
        """编译图"""
        return self.graph.compile(**kwargs)
    
    def get_initial_state(self, message: str, **kwargs) -> Dict[str, Any]:
        """获取初始状态"""
        return {
            "messages": [HumanMessage(content=message)],
            "context": kwargs.get("context", {})
        }


# 便捷函数
def create_agentic_workflow(llm=None):
    """创建智能体工作流实例"""
    if llm is None:
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)
    
    return AgenticWorkflowFramework(llm)


# 使用示例
async def example_usage():
    """示例用法"""
    # 创建智能体工作流
    workflow = create_agentic_workflow()
    compiled_graph = workflow.compile()
    
    # 测试简单对话
    state1 = workflow.get_initial_state("你好")
    result1 = await compiled_graph.ainvoke(state1)
    print("简单对话:", result1["messages"][-1].content)
    
    # 测试查询任务
    state2 = workflow.get_initial_state("查询关于内幕交易的违规案例")
    result2 = await compiled_graph.ainvoke(state2)
    print("查询任务:", result2["messages"][-1].content)


if __name__ == "__main__":
    import asyncio
    print("=== 智能体工作流框架 ===")
    asyncio.run(example_usage())
